
<div class="wp-block-kadence-column kadence-column9_1caaac-fc"><div class="kt-inside-inner-col">    <div class="unified-schema-container">
        <div class="unified-schema-form">
            <form id="unified-schema-form">
                <div class="schema-row">
                    <label for="schema_type">Select Schema Type:</label>
                    <select id="schema_type" name="schema_type">
                        <option value="">Select...</option>
                        <option value="local_business">Local Business</option>
                        <option value="product">Product</option>
                        <option value="product_group">Product Group</option>
                        <option value="youtube_video">YouTube Video</option>
                        <option value="faq">FAQ</option>
                        <option value="recipe">Recipe</option>
                        <option value="software_application">Software/Application</option>
                        <option value="organization">Organization</option>
                        <option value="event">Event</option>
                        <option value="article">Article</option>
                        <option value="rating">Rating</option>
                    </select>
                </div>

                <!-- Local Business Schema Fields -->
                <div id="local_business_fields" class="schema-type-fields" style="display: none;">
                    <div class="schema-row">
                        <label for="lbs_type">Business Type:</label>
                        <select id="lbs_type" name="type">
                                                            <option value="LocalBusiness">LocalBusiness</option>
                                                            <option value="AnimalShelter">AnimalShelter</option>
                                                            <option value="AutomotiveBusiness">AutomotiveBusiness</option>
                                                            <option value="ChildCare">ChildCare</option>
                                                            <option value="Dentist">Dentist</option>
                                                            <option value="DryCleaningOrLaundry">DryCleaningOrLaundry</option>
                                                            <option value="EmergencyService">EmergencyService</option>
                                                            <option value="EmploymentAgency">EmploymentAgency</option>
                                                            <option value="EntertainmentBusiness">EntertainmentBusiness</option>
                                                            <option value="FinancialService">FinancialService</option>
                                                            <option value="FoodEstablishment">FoodEstablishment</option>
                                                            <option value="GovernmentOffice">GovernmentOffice</option>
                                                            <option value="HealthAndBeautyBusiness">HealthAndBeautyBusiness</option>
                                                            <option value="HomeAndConstructionBusiness">HomeAndConstructionBusiness</option>
                                                            <option value="InternetCafe">InternetCafe</option>
                                                            <option value="LegalService">LegalService</option>
                                                            <option value="Library">Library</option>
                                                            <option value="LodgingBusiness">LodgingBusiness</option>
                                                            <option value="MedicalBusiness">MedicalBusiness</option>
                                                            <option value="ProfessionalService">ProfessionalService</option>
                                                            <option value="RadioStation">RadioStation</option>
                                                            <option value="RealEstateAgent">RealEstateAgent</option>
                                                            <option value="RecyclingCenter">RecyclingCenter</option>
                                                            <option value="SelfStorage">SelfStorage</option>
                                                            <option value="ShoppingCenter">ShoppingCenter</option>
                                                            <option value="Store">Store</option>
                                                            <option value="TelevisionStation">TelevisionStation</option>
                                                            <option value="TouristInformationCenter">TouristInformationCenter</option>
                                                            <option value="TravelAgency">TravelAgency</option>
                                                    </select>
                    </div>
                    <div class="schema-row">
                        <label for="lbs_name">Name:</label>
                        <input type="text" id="lbs_name" name="name">
                    </div>
                    <div class="schema-row">
                        <label for="lbs_image">Image URL:</label>
                        <input type="text" id="lbs_image" name="image">
                    </div>
                    <div class="schema-row">
                        <label for="lbs_url">URL:</label>
                        <input type="text" id="lbs_url" name="url">
                    </div>
                    <div class="schema-row">
                        <label for="lbs_phone">Phone:</label>
                        <input type="text" id="lbs_phone" name="phone">
                    </div>
                    <div class="schema-row">
                        <label for="lbs_street">Street:</label>
                        <input type="text" id="lbs_street" name="street">
                    </div>
                    <div class="schema-row">
                        <label for="lbs_city">City:</label>
                        <input type="text" id="lbs_city" name="city">
                    </div>
                    <div class="schema-row">
                        <label for="lbs_state">State/Province/Region:</label>
                        <input type="text" id="lbs_state" name="state">
                    </div>
                    <div class="schema-row">
                        <label for="lbs_zip">Zip Code:</label>
                        <input type="text" id="lbs_zip" name="zip">
                    </div>
                    <div class="schema-row">
                        <label for="lbs_country">Country:</label>
                        <input type="text" id="lbs_country" name="country">
                    </div>
                    <div class="schema-row">
                        <label for="lbs_latitude">Latitude:</label>
                        <input type="text" id="lbs_latitude" name="latitude">
                    </div>
                    <div class="schema-row">
                        <label for="lbs_longitude">Longitude:</label>
                        <input type="text" id="lbs_longitude" name="longitude">
                    </div>

                    <div class="schema-row">
                        <label for="lbs_24x7">Open 24x7:</label>
                        <input type="checkbox" id="lbs_24x7" name="open_24x7">
                    </div>

                    <div id="lbs-opening-hours" class="schema-row">
                        <label>Opening Hours:</label>
                                                    <div class="lbs-day">
                                <label>Monday:</label>
                                <input type="time" id="open_monday" name="open_monday">
                                <input type="time" id="close_monday" name="close_monday">
                            </div>
                                                    <div class="lbs-day">
                                <label>Tuesday:</label>
                                <input type="time" id="open_tuesday" name="open_tuesday">
                                <input type="time" id="close_tuesday" name="close_tuesday">
                            </div>
                                                    <div class="lbs-day">
                                <label>Wednesday:</label>
                                <input type="time" id="open_wednesday" name="open_wednesday">
                                <input type="time" id="close_wednesday" name="close_wednesday">
                            </div>
                                                    <div class="lbs-day">
                                <label>Thursday:</label>
                                <input type="time" id="open_thursday" name="open_thursday">
                                <input type="time" id="close_thursday" name="close_thursday">
                            </div>
                                                    <div class="lbs-day">
                                <label>Friday:</label>
                                <input type="time" id="open_friday" name="open_friday">
                                <input type="time" id="close_friday" name="close_friday">
                            </div>
                                                    <div class="lbs-day">
                                <label>Saturday:</label>
                                <input type="time" id="open_saturday" name="open_saturday">
                                <input type="time" id="close_saturday" name="close_saturday">
                            </div>
                                                    <div class="lbs-day">
                                <label>Sunday:</label>
                                <input type="time" id="open_sunday" name="open_sunday">
                                <input type="time" id="close_sunday" name="close_sunday">
                            </div>
                                            </div>

                    <div id="lbs-social-profiles" class="schema-row">
                        <label>Social Profiles:</label>
                        <input type="text" id="social_facebook" name="social_facebook" placeholder="Facebook URL">
                        <input type="text" id="social_twitter" name="social_twitter" placeholder="Twitter URL">
                        <input type="text" id="social_instagram" name="social_instagram" placeholder="Instagram URL">
                        <input type="text" id="social_linkedin" name="social_linkedin" placeholder="LinkedIn URL">
                    </div>
                </div>

                <!-- Product Schema Fields -->
                <div id="product_fields" class="schema-type-fields" style="display: none;">
                    <div class="schema-row">
                        <label for="product_name">Product Name:</label>
                        <input type="text" id="product_name" name="product_name">
                    </div>
                    <div class="schema-row">
                        <label for="product_description">Description:</label>
                        <textarea id="product_description" name="product_description"></textarea>
                    </div>
                    <div class="schema-row">
                        <label for="product_image">Image URL:</label>
                        <input type="text" id="product_image" name="product_image">
                    </div>
                    <div class="schema-row">
                        <label for="product_sku">SKU:</label>
                        <input type="text" id="product_sku" name="product_sku">
                    </div>
                    <div class="schema-row">
                        <label for="product_brand">Brand:</label>
                        <input type="text" id="product_brand" name="product_brand">
                    </div>
                    <div class="schema-row">
                        <label for="product_price">Price:</label>
                        <input type="text" id="product_price" name="product_price">
                    </div>
                    <div class="schema-row">
                        <label for="product_shipping">Shipping Cost:</label>
                        <input type="text" id="product_shipping" name="product_shipping">
                    </div>
                    <div class="schema-row">
                        <label for="product_returnPolicy">Merchant Return Policy URL:</label>
                        <input type="text" id="product_returnPolicy" name="product_returnPolicy">
                    </div>
                </div>

                <!-- Product Group Schema Fields -->
                <div id="product_group_fields" class="schema-type-fields" style="display: none;">
                    <div class="schema-row">
                        <label for="pg_name">Product Group Name:</label>
                        <input type="text" id="pg_name" name="pg_name">
                    </div>
                    <div class="schema-row">
                        <label for="pg_description">Description:</label>
                        <textarea id="pg_description" name="pg_description"></textarea>
                    </div>
                    <div class="schema-row">
                        <label for="pg_image">Image URL:</label>
                        <input type="text" id="pg_image" name="pg_image">
                    </div>
                    <div class="schema-row">
                        <label for="pg_brand">Brand:</label>
                        <input type="text" id="pg_brand" name="pg_brand">
                    </div>
                    <div class="schema-row">
                        <label for="pg_productGroupID">Product Group ID:</label>
                        <input type="text" id="pg_productGroupID" name="pg_productGroupID">
                    </div>
                    <div class="schema-row">
                        <label for="pg_variesBy">Varies By:</label>
                        <select id="pg_variesBy" name="pg_variesBy">
                            <option value="https://schema.org/color">Color</option>
                            <option value="https://schema.org/size">Size</option>
                            <option value="https://schema.org/suggestedGender">Suggested Gender</option>
                            <option value="https://schema.org/suggestedAge">Suggested Age</option>
                            <option value="https://schema.org/material">Material</option>
                            <option value="https://schema.org/pattern">Pattern</option>
                        </select>
                    </div>

                    <div id="product-variants">
                        <h4>Variants:</h4>
                        <div class="variant-entry">
                            <label for="variant_name_1">Variant Name:</label>
                            <input type="text" id="variant_name_1" name="variant_name_1">
                            <label for="variant_description_1">Description (optional):</label>
                            <textarea id="variant_description_1" name="variant_description_1"></textarea>
                            <label for="variant_image_1">Image URL:</label>
                            <input type="text" id="variant_image_1" name="variant_image_1">
                            <label for="variant_color_1">Color:</label>
                            <input type="text" id="variant_color_1" name="variant_color_1">
                            <label for="variant_size_1">Size:</label>
                            <input type="text" id="variant_size_1" name="variant_size_1">
                            <label for="variant_sku_1">SKU:</label>
                            <input type="text" id="variant_sku_1" name="variant_sku_1">
                            <label for="variant_price_1">Price:</label>
                            <input type="text" id="variant_price_1" name="variant_price_1">
                            <label for="variant_shipping_1">Shipping Cost:</label>
                            <input type="text" id="variant_shipping_1" name="variant_shipping_1">
                        </div>
                        <button type="button" id="add-variant">Add Another Variant</button>
                    </div>

                    <div class="schema-row">
                        <label for="pg_returnPolicy">Merchant Return Policy URL:</label>
                        <input type="text" id="pg_returnPolicy" name="pg_returnPolicy">
                    </div>
                </div>

                <!-- YouTube Video Schema Fields -->
                <div id="youtube_video_fields" class="schema-type-fields" style="display: none;">
                    <div class="schema-row">
                        <label for="schema_mode">Select Mode:</label>
                        <select id="schema_mode" name="schema_mode">
                            <option value="auto">Automatic</option>
                            <option value="manual">Manual</option>
                        </select>
                    </div>
                    <div id="auto_fields">
                        <div class="schema-row">
                            <label for="youtube_url">YouTube Video URL:</label>
                            <input type="text" id="youtube_url" name="youtube_url" placeholder="Enter YouTube video URL">
                        </div>
                        <button type="button" id="fetch-youtube-data">Fetch Video Details</button>
                    </div>
                    <div id="manual_fields" style="display: none;">
                        <div class="schema-row">
                            <label for="video_name">Video Name:</label>
                            <input type="text" id="video_name" name="video_name" placeholder="Enter video name">
                        </div>
                        <div class="schema-row">
                            <label for="video_description">Description:</label>
                            <textarea id="video_description" name="video_description" placeholder="Enter video description"></textarea>
                        </div>
                        <div class="schema-row">
                            <label for="video_thumbnail">Thumbnail URL:</label>
                            <input type="text" id="video_thumbnail" name="video_thumbnail" placeholder="Enter thumbnail URL">
                        </div>
                        <div class="schema-row">
                            <label for="video_upload_date">Upload Date:</label>
                            <input type="text" id="video_upload_date" name="video_upload_date" placeholder="Enter upload date (YYYY-MM-DD)">
                        </div>
                        <div class="schema-row">
                            <label for="video_duration">Duration:</label>
                            <input type="text" id="video_duration" name="video_duration" placeholder="Enter duration (PT#M#S)">
                        </div>
                        <div class="schema-row">
                            <label for="video_embed_url">Embed URL:</label>
                            <input type="text" id="video_embed_url" name="video_embed_url" placeholder="Enter embed URL">
                        </div>
                        <div class="schema-row">
                            <label for="video_interaction_count">Interaction Count:</label>
                            <input type="text" id="video_interaction_count" name="video_interaction_count" placeholder="Enter interaction count">
                        </div>
                        <button type="button" id="generate-manual-schema">Generate Schema</button>
                    </div>
                </div>

                <!-- FAQ Schema Fields -->
                <div id="faq_fields" class="schema-type-fields" style="display: none;">
                    <div id="faq-entries">
                        <div class="faq-entry">
                            <label for="question_1">Question:</label>
                            <input type="text" id="question_1" name="question_1">
                            <label for="answer_1">Answer:</label>
                            <textarea id="answer_1" name="answer_1"></textarea>
                        </div>
                    </div>
                    <button type="button" id="add-faq">Add Another FAQ</button>
                </div>

                <!-- Recipe Schema Fields -->
                <div id="recipe_fields" class="schema-type-fields" style="display: none;">
                    <div class="schema-row">
                        <label for="recipe_name">Recipe Name:</label>
                        <input type="text" id="recipe_name" name="recipe_name" required>
                    </div>
                    <div class="schema-row">
                        <label for="recipe_author">Author Name:</label>
                        <input type="text" id="recipe_author" name="recipe_author" required>
                    </div>
                    <div class="schema-row">
                        <label for="recipe_image">Image URL:</label>
                        <input type="url" id="recipe_image" name="recipe_image" required>
                    </div>
                    <div class="schema-row">
                        <label for="recipe_description">Description:</label>
                        <textarea id="recipe_description" name="recipe_description" required></textarea>
                    </div>
                    <div class="schema-row">
                        <label for="recipe_prepTime">Prep Time (e.g., PT15M):</label>
                        <input type="text" id="recipe_prepTime" name="recipe_prepTime" required>
                    </div>
                    <div class="schema-row">
                        <label for="recipe_cookTime">Cook Time (e.g., PT10M):</label>
                        <input type="text" id="recipe_cookTime" name="recipe_cookTime" required>
                    </div>
                    <div class="schema-row">
                        <label for="recipe_totalTime">Total Time (e.g., PT25M):</label>
                        <input type="text" id="recipe_totalTime" name="recipe_totalTime" required>
                    </div>
                    <div class="schema-row">
                        <label for="recipe_keywords">Keywords (comma separated):</label>
                        <input type="text" id="recipe_keywords" name="recipe_keywords">
                    </div>
                    <div class="schema-row">
                        <label for="recipe_yield">Recipe Yield:</label>
                        <input type="text" id="recipe_yield" name="recipe_yield" required>
                    </div>
                    <div class="schema-row">
                        <label for="recipe_category">Recipe Category:</label>
                        <input type="text" id="recipe_category" name="recipe_category" required>
                    </div>
                    <div class="schema-row">
                        <label for="recipe_cuisine">Recipe Cuisine:</label>
                        <input type="text" id="recipe_cuisine" name="recipe_cuisine" required>
                    </div>
                    <div class="schema-row">
                        <label for="recipe_calories">Calories:</label>
                        <input type="text" id="recipe_calories" name="recipe_calories" required>
                    </div>
                    <div class="schema-row">
                        <label for="recipe_fatContent">Fat Content:</label>
                        <input type="text" id="recipe_fatContent" name="recipe_fatContent" required>
                    </div>
                    <div class="schema-row">
                        <label for="recipe_ingredients">Ingredients (one per line):</label>
                        <textarea id="recipe_ingredients" name="recipe_ingredients" required></textarea>
                    </div>
                    <div class="schema-row">
                        <label for="recipe_instructions">Instructions (one per line):</label>
                        <textarea id="recipe_instructions" name="recipe_instructions" required></textarea>
                    </div>
                </div>

                <!-- Software/Application Schema Fields -->
                <div id="software_application_fields" class="schema-type-fields" style="display: none;">
                    <div class="schema-row">
                        <label for="app_name">Application Name:</label>
                        <input type="text" id="app_name" name="name" required>
                    </div>
                    <div class="schema-row">
                        <label for="app_description">Description:</label>
                        <textarea id="app_description" name="description" required></textarea>
                    </div>
                    <div class="schema-row">
                        <label for="app_author">Author Name:</label>
                        <input type="text" id="app_author" name="author">
                    </div>
                    <div class="schema-row">
                        <label for="app_operatingSystem">Operating System:</label>
                        <input type="text" id="app_operatingSystem" name="operatingSystem">
                    </div>
                    <div class="schema-row">
                        <label for="app_applicationCategory">Application Category:</label>
                        <input type="text" id="app_applicationCategory" name="applicationCategory">
                    </div>
                    <div class="schema-row">
                        <label for="app_offers">Price (e.g., 0.00 for free):</label>
                        <input type="text" id="app_offers" name="offers">
                    </div>
                    <div class="schema-row">
                        <label for="app_downloadUrl">Download URL:</label>
                        <input type="url" id="app_downloadUrl" name="downloadUrl">
                    </div>
                </div>

                <!-- Organization Schema Fields -->
                <div id="organization_fields" class="schema-type-fields" style="display: none;">
                    <div class="schema-row">
                        <label for="org_name">Organization Name:</label>
                        <input type="text" id="org_name" name="name" required>
                    </div>
                    <div class="schema-row">
                        <label for="org_description">Description:</label>
                        <textarea id="org_description" name="description" required></textarea>
                    </div>
                    <div class="schema-row">
                        <label for="org_type">Organization Type:</label>
                        <input type="text" id="org_type" name="organizationType">
                    </div>
                    <div class="schema-row">
                        <label for="org_url">URL:</label>
                        <input type="url" id="org_url" name="url">
                    </div>
                    <div class="schema-row">
                        <label for="org_contactPoint">Contact Point:</label>
                        <input type="text" id="org_contactPoint" name="contactPoint">
                    </div>
                </div>

                <!-- Event Schema Fields -->
                <div id="event_fields" class="schema-type-fields" style="display: none;">
                    <div class="schema-row">
                        <label for="event_name">Event Name:</label>
                        <input type="text" id="event_name" name="name" required>
                    </div>
                    <div class="schema-row">
                        <label for="event_description">Description:</label>
                        <textarea id="event_description" name="description" required></textarea>
                    </div>
                    <div class="schema-row">
                        <label for="event_startDate">Start Date:</label>
                        <input type="date" id="event_startDate" name="startDate">
                    </div>
                    <div class="schema-row">
                        <label for="event_endDate">End Date:</label>
                        <input type="date" id="event_endDate" name="endDate">
                    </div>
                    <div class="schema-row">
                        <label for="event_location">Location:</label>
                        <input type="text" id="event_location" name="location">
                    </div>
                    <div class="schema-row">
                        <label for="event_status">Event Status:</label>
                        <input type="text" id="event_status" name="eventStatus">
                    </div>
                </div>

                <!-- Article Schema Fields -->
                <div id="article_fields" class="schema-type-fields" style="display: none;">
                    <div class="schema-row">
                        <label for="article_author">Author Name:</label>
                        <input type="text" id="article_author" name="author" required>
                    </div>
                    <div class="schema-row">
                        <label for="article_headline">Headline:</label>
                        <input type="text" id="article_headline" name="headline" required>
                    </div>
                    <div class="schema-row">
                        <label for="article_datePublished">Date Published:</label>
                        <input type="date" id="article_datePublished" name="datePublished" required>
                    </div>
                    <div class="schema-row">
                        <label for="article_dateModified">Date Modified:</label>
                        <input type="date" id="article_dateModified" name="dateModified">
                    </div>
                    <div class="schema-row">
                        <label for="article_body">Article Body:</label>
                        <textarea id="article_body" name="articleBody" required></textarea>
                    </div>
                    <div class="schema-row">
                        <label for="article_mainEntityOfPage">Main Entity of Page URL:</label>
                        <input type="url" id="article_mainEntityOfPage" name="mainEntityOfPage">
                    </div>
                </div>
                
                <!-- Aggregate Rating Schema Fields -->
                <div id="rating_fields" class="schema-type-fields" style="display: none;">
                    <div class="schema-row">
                        <label for="rating_schema_type">Select Schema Type:</label>
                        <select id="rating_schema_type" name="rating_schema_type">
                            <option value="">Select...</option>
                            <option value="aggregate_rating">Aggregate Rating</option>
                            <option value="review_rating">Review Rating</option>
                        </select>
                    </div>

                    <!-- Aggregate Rating Schema Fields -->
                    <div id="aggregate_rating_fields" class="rating-schema-type-fields" style="display: none;">
                        <div class="schema-row">
                            <label for="ar_item_name">Item Name:</label>
                            <input type="text" id="ar_item_name" name="ar_item_name" required>
                        </div>
                        <div class="schema-row">
                            <label for="ar_rating_value">Rating Value:</label>
                            <input type="number" id="ar_rating_value" name="ar_rating_value" required min="0" step="0.1">
                        </div>
                        <div class="schema-row">
                            <label for="ar_best_rating">Best Rating:</label>
                            <input type="number" id="ar_best_rating" name="ar_best_rating" required min="0">
                        </div>
                        <div class="schema-row">
                            <label for="ar_rating_count">Rating Count:</label>
                            <input type="number" id="ar_rating_count" name="ar_rating_count" required min="0">
                        </div>
                    </div>
    
                    <!-- Review Rating Schema Fields -->
                    <div id="review_rating_fields" class="rating-schema-type-fields" style="display: none;">
                        <div class="schema-row">
                            <label for="rr_item_name">Item Name:</label>
                            <input type="text" id="rr_item_name" name="rr_item_name" required>
                        </div>
                        <div class="schema-row">
                            <label for="rr_review_body">Review Body:</label>
                            <textarea id="rr_review_body" name="rr_review_body" required></textarea>
                        </div>
                        <div class="schema-row">
                            <label for="rr_author_name">Author Name:</label>
                            <input type="text" id="rr_author_name" name="rr_author_name" required>
                        </div>
                        <div class="schema-row">
                            <label for="rr_rating_value">Rating Value:</label>
                            <input type="number" id="rr_rating_value" name="rr_rating_value" required min="0" step="0.1">
                        </div>
                        <div class="schema-row">
                            <label for="rr_best_rating">Best Rating:</label>
                            <input type="number" id="rr_best_rating" name="rr_best_rating" required min="0">
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
</div></div>

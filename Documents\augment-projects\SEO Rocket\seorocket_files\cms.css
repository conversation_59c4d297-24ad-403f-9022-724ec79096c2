body {
	font-family: 'GoodHeadlinePro', sans-serif;
}

.filter-label svg path {
	fill: #2E2E2E;
	transition: .3s;
}

.filter-label :checked~span {
	background-color: #2E2E2E;
	color: #fff;
}

.filter-label:hover input:not(:checked)~span {
	background-color: #2e2e2e9c;
	color: #fff;
}

.filter-label:hover span svg path,
.filter-label :checked~span svg path {
	fill: #fff;
}


.toggle-radio {
	display: inline;
	position: relative;
	overflow: hidden;
	cursor: pointer;
}

.toggle-radio p {
	padding: 10px;
	margin: 0;
	float: left;
	line-height: 20px;
}

.toggle-radio input {
	position: absolute;
	visibility: hidden;
}

.toggle-radio input+i {
	border: 2px solid #ccc;
	width: 56px;
	height: 32px;
	padding: 2px;
	float: left;
	border-radius: 20px;
	transition: all .25s;
}

.toggle-radio input+i:after {
	content: ' ';
	background-color: #ccc;
	float: left;
	width: 50%;
	height: 100%;
	border-radius: inherit;
	transition: inherit;
	border-radius: 50%;
}

.onOff input+i {
	border: 2px solid #AEAEAE;
	background: #AEAEAE;
}

.onOff input+i:after {
	background-color: #FFFFFF;
}

.toggle-radio input:checked+i {
	border-color: #2ab70e;
	background-color: #2ab70e;
}

.toggle-radio input:checked+i:after {
	background-color: #ffff;
	margin-left: 50%;
}

.checkbox-with-border :checked~* {
	border-color: #2E2E2E;
}

.checkbox-with-border :checked~* .check img {
	transform: rotate(0);
	opacity: 1;
}

.show-menu-bar [page-wrapper] {
	padding-inline-start: 60px;
	padding-inline-end: 10px;
}

[side-bar] nav li.active svg path {
	stroke: #e40b3e;
}

[side-bar] nav li.active .keyword-density-icon path {
	fill: #e40b3e;
}

.search-operation-iframe iframe {
	box-shadow: none !important;
	border-radius: 20px !important;
	height: 440px;
}

.nice-select {
	display: flex;
	align-items: center;
}

.nice-select .current {
	font-size: 16px;
}

.nice-select .list {
	max-height: 340px;
	overflow-y: auto;
	width: 100%;
}

[tab-name] {
	display: none;
}

[keyword-item]:nth-of-type(2) [circle] {
	background-color: #4285f4;
}

[keyword-item]:nth-of-type(3) [circle] {
	background-color: #db4537;
}

[keyword-item]:nth-of-type(4) [circle] {
	background-color: #f5b401;
}

[keyword-item]:nth-of-type(5) [circle] {
	background-color: #0e9d59;
}

[keyword-item]:nth-of-type(6) [circle] {
	background-color: #ab47bc;
}

.related-queries-inside iframe {
	height: 397px;
}

.related-queries-inside iframe iframe {
	height: 100%;
	box-shadow: rgba(0, 0, 0, 0.12) 0px 0px 2px 0px, rgba(0, 0, 0, 0.24) 0px 2px 2px 0px;
	border-radius: 2px;
}

.checkbox-with-border .spna {
	gap: 14px;
}

.checkbox-with-border .spna [circle] {
	width: 10px;
	height: 10px;
}

.checkbox-with-border .shell {
	border: 1px solid #ECECEC;
	border-radius: 6px;
}

[tab-3-field] {
	display: none;
}

[data-operation="allintitle"] [tab-3-field="domain"],
[data-operation="allinurl"] [tab-3-field="domain"] {
	display: none;
}

[tab-3-field="domain"],
[data-operation="insite"] [tab-3-field="domain"],
[data-operation="allintitle"] [tab-3-field="keyword"],
[data-operation="allinurl"] [tab-3-field="keyword"],
[data-operation="KGR"] [tab-3-field="market"] {
	display: block;
}

[error-message] {
	display: block;
	/* width: 100px; */
}

.sidemenu-active [side-bar] {
	width: 241px;
}

.sidemenu-active [page-wrapper] {
	padding-inline-start: 272px;
}

.sidemenu-active [toggle-side-menu] {
	transform: rotate(180deg);
}

.full-checkbox input {
	display: none;
}

.full-checkbox span {
	position: relative;
	display: flex;
	align-items: center;
	gap: 10px;
	border-radius: 2px;
	cursor: pointer;
}

.full-checkbox span:before {
	content: '';
	display: inline-block;
	border: 1px solid #E3E3E3;
	background-color: #ffff;
	width: 24px;
	height: 24px;
	border-radius: 2px;
}

.full-checkbox :checked~span:before {
	background-color: #E40B3E;
}

select[init-select]~.select2 .select2-selection {
	height: 55px;
	border-radius: 8px;
	border: 0;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
	display: flex;
	align-items: center;
	height: 100%;
	color: #8F8F8F;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
	position: absolute;
	top: 0;
	bottom: 0;
	margin: auto;
}

span.select2-selection__arrow b {
	display: none;
}

span.select2-selection__arrow:after {
	border-bottom: 2px solid #999;
	border-right: 2px solid #999;
	content: "";
	display: block;
	height: 5px;
	margin-top: -4px;
	pointer-events: none;
	position: absolute;
	right: 12px;
	top: 50%;
	-webkit-transform-origin: 66% 66%;
	-ms-transform-origin: 66% 66%;
	transform-origin: 66% 66%;
	-webkit-transform: rotate(45deg);
	-ms-transform: rotate(45deg);
	transform: rotate(45deg);
	-webkit-transition: all 0.15s ease-in-out;
	transition: all 0.15s ease-in-out;
	width: 5px;
}

.select2-container {
	width: 100% !important;
}

@media (max-width: 1024px) {
	[side-bar] {
		transform: translateX(-100%);
		width: 36px;
	}

	.show-menu-bar [page-wrapper] {
		padding-inline-start: 50px;
		padding-inline-end: 10px;
	}

	.show-menu-bar [side-bar] {
		transform: translateX(0%);
	}

	[toggle-side-menu-li] {
		display: none;
	}
}

.CardHelp {
	display: flex;
	width: 33.3%;
	max-width: 400px;
	min-height: 400px;
	padding: 90px 67px;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	gap: 11px;
	border-radius: 8px;
	border: 2px solid #818181;
	background: #FFF;
}

.CardHelp:hover {
	border: 2px solid #2E2E2E;
	background: #F3F2F2;
}

.headerCard {
	color: #2E2E2E;
	font-size: 24px;
	font-style: normal;
	font-weight: 500;
	line-height: normal;
}

.ContentCard {
	color: #797978;
	text-align: center;
	font-size: 14px;
	font-style: normal;
	font-weight: 400;
	line-height: 20px;
	max-width: 334px;
}

.d-flex {
	display: flex;
	display: -webkit-flex;
}

.d-grid {
	display: grid;
}

.d-none {
	display: none;
}

.flex-column {
	flex-direction: column;
}

.flex-row {
	flex-direction: row;
}

.gap-90 {
	gap: 90px;
}

.gap-50 {
	gap: 50px;
}

.gap-40 {
	gap: 40px;
}

.gap-32 {
	gap: 32px;
}

.gap-10 {
	gap: 10px;
}

.wrap {
	flex-wrap: wrap;
}

.gray {
	color: rgba(119, 119, 119, 1);
}

.pathDes .black {
	color: rgb(0, 0, 0);
}

.font-16 {
	font-size: 16px;
}

.bold {
	font-weight: bold;
}
.step {
	display: grid;
    grid-template-columns: 300px auto;
	font-size: 20px;
}

.step .secStep {
	display: grid;
	grid-template-columns: 15px auto;
	height: fit-content;
    margin: auto;
}


@media screen and (max-width: 767px){
	.step {
		grid-template-columns: auto;
	}
}
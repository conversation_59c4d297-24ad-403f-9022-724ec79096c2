const GOOGLE_COLORS = ['#4285f4','#db4537','#f5b401','#0e9d59','#ab47bc']
$(document).ready(function(){
    init_select()
    

    keyword_density_page()
    search_operation_page()

    forms_validation()


    toggle_side_menu()
});

function init_select() {
    if ($('[init-select]').length > 0) {
        $('[init-select]').each(function(){
            $(this).select2();
        })
    }
}

function search_operation_page() {
    function init_iframe(){
        var iframe = document.getElementById('google_iframe');
        if (iframe) {
            var iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            // Optionally clear existing content in the body
            iframeDoc.body.innerHTML = '';
            var externalScript = iframeDoc.createElement('script');
            externalScript.type = 'text/javascript';
            externalScript.src = 'https://ssl.gstatic.com/trends_nrtr/3826_RC01/embed_loader.js';
            
            iframeDoc.body.appendChild(externalScript);
        }
        var iframe2 = document.getElementById('google_iframe2');
        if (iframe2) {
            var iframeDoc = iframe2.contentDocument || iframe2.contentWindow.document;
            // Optionally clear existing content in the body
            iframeDoc.body.innerHTML = '';
            var externalScript = iframeDoc.createElement('script');
            externalScript.type = 'text/javascript';
            externalScript.src = 'https://ssl.gstatic.com/trends_nrtr/3826_RC01/embed_loader.js';
            
            iframeDoc.body.appendChild(externalScript);
        }
        var iframe3 = document.getElementById('google_iframe3');
        if (iframe3) {
            var iframeDoc = iframe3.contentDocument || iframe3.contentWindow.document;
            // Optionally clear existing content in the body
            iframeDoc.body.innerHTML = '';
            var externalScript = iframeDoc.createElement('script');
            externalScript.type = 'text/javascript';
            externalScript.src = 'https://ssl.gstatic.com/trends_nrtr/3826_RC01/embed_loader.js';
            
            iframeDoc.body.appendChild(externalScript);
        }
        var iframe4 = document.getElementById('google_iframe4');
        if (iframe4) {
            var iframeDoc = iframe4.contentDocument || iframe4.contentWindow.document;
            // Optionally clear existing content in the body
            iframeDoc.body.innerHTML = '';
            var externalScript = iframeDoc.createElement('script');
            externalScript.type = 'text/javascript';
            externalScript.src = 'https://ssl.gstatic.com/trends_nrtr/3826_RC01/embed_loader.js';
            
            iframeDoc.body.appendChild(externalScript);
        }
    }

    function clearIframe(iframeId) {
        var iframe = document.getElementById(iframeId);
        if (iframe) {
            var iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
    
            // Clear existing content in the body
            iframeDoc.body.innerHTML = '';
        }
    }

    function applyFiltersToIframe(iframeId, widgetType,geo, comparisonItems, category, searchType) {
        var iframe = document.getElementById(iframeId);
        if (iframe) {
            var iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
    
            // Clear existing content in the body
            iframeDoc.body.innerHTML = '';

            let keywords = comparisonItems.map(item => item.keyword).join(',');
            const times = comparisonItems.map(item => item.time).join(',');
            // let exploreQuery = `hl=en-US&date=${times}`;
            let exploreQuery = ``;
            if (comparisonItems.length == 0) {
                let obj = {};
                const time = $('[search-ddl-filter="date"]').val();
                if (time != '') {
                    exploreQuery = `date=${time}`;
                    obj.time = time;
                } else {
                    exploreQuery = `date=today 12-m`;
                    obj.time = 'today 12-m';
                }

                // const geoVal = $('[search-ddl-filter="geo"]').val();
                obj.geo = geo;
                
                comparisonItems.push(obj);
            } else {
                exploreQuery = `date=${times}`;
            }
            
            if (keywords.length > 0) {
                exploreQuery = `q=${keywords}&${exploreQuery}`;
            }
            if (category && category !== 0) {
                exploreQuery += `&cat=${category}`;
            }
            
            if (geo) {
                exploreQuery += `&geo=${geo}`;
            }
            
            if (searchType) {
                exploreQuery += `&gprop=${searchType}`;
            }
            var widgetScript = iframeDoc.createElement('script');
            widgetScript.type = 'text/javascript';
            widgetScript.innerHTML = `
                trends.embed.renderExploreWidget("${widgetType}", {
                "comparisonItem": ${JSON.stringify(comparisonItems)},
                "category":${category},
                "property":"${searchType || ''}"
                }, {
                "exploreQuery":"${exploreQuery}",
                "guestPath":"https://trends.google.com:443/trends/embed/"
                });
            `;
            var styleTag = iframeDoc.createElement('style');
            styleTag.type = 'text/css';
            styleTag.innerHTML = `
                iframe {
                    height: 100%;
                    box-shadow: none !important;
                    border-radius: 20px !important;
                }
            `;
            
            iframeDoc.body.appendChild(widgetScript);
            iframeDoc.head.appendChild(styleTag);

            let trendsUrl = document.getElementById("gtrend-link");
            trendsUrl.href = 'https://trends.google.com/trends/explore?' + exploreQuery;

        }
    }

    function loadData() {
        var filters = {};
        const category = $('[search-ddl-filter="cat"]').val() || 0;
        const date = $('[search-ddl-filter="date"]').val();
        const geo = $('[search-ddl-filter="geo"]').val();
        const searchType = $('[search-ddl-filter="gprop"]').val(); // web search, news etc ...

        var keywordsCount = $('[keywords-box] [keyword-item]').length;
        if (keywordsCount == 0) {
            clearIframe('google_iframe')
            clearIframe('google_iframe4')
            $('[keyword-search-only]').hide()
        } else {
            $('[keyword-search-only]').show()
        }

        $('[search-ddl-filter]').each(function() {
            var filterType = $(this).attr('search-ddl-filter');
            filters[filterType] = $(this).val();
        });

        var comparisonItems = [];
        $('[keyword-item]').each(function() {
            var val = $(this).find('[value]').text();
            const item = {
                keyword: val,
                geo: geo || '',
                time: date
            };
            comparisonItems.push(item);
        });
        
        applyFiltersToIframe('google_iframe', 'GEO_MAP',geo, comparisonItems, category, searchType);

        applyFiltersToIframe('google_iframe2', 'RELATED_TOPICS',geo, comparisonItems, category, searchType);

        applyFiltersToIframe('google_iframe3', 'RELATED_QUERIES',geo, comparisonItems, category, searchType);
        
        applyFiltersToIframe('google_iframe4', 'TIMESERIES',geo, comparisonItems, category, searchType);
        
    }

    function collectKeywords() {
        var newKeyword = $('[keyword-filter]').val().trim();
        var keywordsCount = $('[keywords-box] [keyword-item]').length;

        if (newKeyword == '' || keywordsCount >= 5) return false;
        
        var template = ``;
        $('[keyword-filter]').val('');
        if (keywordsCount == 0) {
            template += `
            <label class="checkbox-with-border flex flex-wrap gap-2 md:gap-4" keyword-clear-box>
                <input keyword-clear checked type="checkbox" class="hidden" />
                <div class="shell duration-300 border py-1 md:ps-2.5 md:pe-4 px-2 relative flex items-center gap-2 md:gap-4 border-[#ECECEC] rounded-[6px] cursor-pointer bg-white">
                    <span class="border border-[#B8B8BC] rounded-[4px] shrink-0 w-4 h-4 flex items-center justify-center check duration-300">
                        <img src="/assets/images/check.svg" class="duration-300 opacity-0 w-2 rotate-180" />
                    </span>
                    <span class="text-[#8F8F8F] text-sm md:text-[19px]">All</span>
                </div>
            </label>
            `;
        }

        template += `
            <label class="checkbox-with-border flex flex-wrap gap-2 md:gap-4" keyword-item>
                <input type="checkbox" checked class="hidden" />
                <div class="shell duration-300 border py-1.5 md:ps-2.5 md:pe-4 px-2 relative flex items-center gap-2 md:gap-4 border-[#ECECEC] rounded-[6px] cursor-pointer bg-white">
                    <span class="border border-[#B8B8BC] rounded-[4px] shrink-0 w-4 h-4 flex items-center justify-center check duration-300">
                        <img src="/assets/images/check.svg" class="duration-300 opacity-0 w-2 rotate-180" />
                    </span>
                    <div class="flex spna items-center">
                        <span circle class="rounded-full w-2.5 h-2.5  bg-[#FF0000]"></span>
                        <span class="text-[#8F8F8F] text-sm md:text-[19px]" value>${newKeyword}</span>
                    </div>
                </div>
            </label>
        `

        $('[keywords-box]').append(template);
        return true;
    }

    function event_listeners() {
        $('[submit-filters-btn]').on('click', function() {
            var isValid = collectKeywords();
            if (isValid) {
                loadData()
            }
        });
        $('[search-ddl-filter]').on('change', function() {
            loadData()
        });
        $('body').on('change','[keyword-item] input',function() {
            var root = $(this).parent('[keyword-item]');
            $(root).remove();

            var keywordsCount = $('[keywords-box] [keyword-item]').length;

            if (keywordsCount == 0) {
                $('[keyword-clear-box]').remove();
            }
            loadData()
        });
        $('body').on('change','[keyword-clear]',function() {
            $('[keywords-box]').html('');
            loadData();
        });
        $('[keyword-filter]').on('keypress', function(e) {
            var val = $(this).val().trim();
            if (e.which === 13 && val != '') { // 13 is the Enter key code
                var isValid = collectKeywords();
                if (isValid) {
                    loadData()
                }
            }
        });
    } 

    

    init_iframe();
    event_listeners()
}

function keyword_density_page() {
    if (document.querySelector('[keyword-density-page]')) {
        function loadURL() {
            var urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has('tab')) {
                var tab = urlParams.get('tab');
                var $tabElement = $('[keyword-type="' + tab + '"]');
                
                if ($tabElement.length) {
                    $('[tab-name]').hide();
                    $('[tab-name*="' + tab + '"]').fadeIn();
                    $('[keyword-type="' + tab + '"]').prop('checked', true);
                }
            }
        }
        
        function select_onChange() {
            $('[keyword-type]').on('change', function() {
                var selectedTab = $(this).attr('keyword-type');
                $('[tab-name]').hide();
                $('[tab-name*="' + selectedTab + '"]').fadeIn();
                var newUrl = window.location.protocol + "//" + window.location.host + window.location.pathname + '?tab=' + selectedTab;
                history.replaceState(null, null, newUrl);
            });

            $('[onChange-search-operator]').on('change', function() {
                var currentValue = $(this).val();
                
                // Hide all fields initially
                $('[tab-3-field]').hide();
                
                // Show the fields based on the selected option
                if (currentValue === 'insite') {
                    $('[tab-3-field="domain"]').show();
                } else if (currentValue === 'allinurl' || currentValue === 'allintitle') {
                    $('[tab-3-field="keyword"]').show();
                } else if (currentValue === 'KGR') {
                    $('[tab-3-field="keyword"]').show();
                    $('[tab-3-field="market"]').show();
                } else if (!currentValue) {
                    $('[tab-3-field="domain"]').show();
                }
                
                // Set required attributes
                document.querySelectorAll('[tab-3-field] [is-req]').forEach(function(element) {
                    element.removeAttribute('required');
                });
                document.querySelectorAll('[tab-3-field="' + currentValue + '"] [is-req]').forEach(function(element) {
                    element.setAttribute('required', 'required');
                });
            });

            $('[onChange-search-operator]').change(); // Trigger initial change event
        }

        loadURL();
        select_onChange();
    }
}


function showField__keyDensityPage(){
    // const searchOperators = document.getElementById("operation");
    // const keyword = document.getElementById("keyword-container");
    // const keyword_input = document.querySelector("#keyword-container input");
    // const site = document.getElementById("domain-container");
    // const site_input = document.querySelector("#domain-container input");
    // const market = document.getElementById("market-container")
    // const market_select = document.querySelector("#market-container select");
    
    // if (searchOperators.value == 'insite'){
    //     site.classList.remove('hide');
    //     keyword.classList.add('hide')
    //     site_input.name = 'domain';
    //     keyword_input.name = '';
    //     market.classList.add('hide');
    //     market_select.name = '';
    // }else if(searchOperators.value == 'KGR'){
    //     site.classList.add('hide');
    //     keyword.classList.remove('hide');
    //     keyword_input.name = 'keyword';
    //     market.classList.remove('hide');
    //     market_select.name = 'market';
    //     site_input.name = '';
    // }else{
    //     site.classList.add('hide');
    //     keyword.classList.remove('hide');
    //     keyword_input.name = 'keyword';
    //     site_input.name = '';
    //     market.classList.add('hide');
    //     market_select.name = '';
    // }
}

function forms_validation() {
    // $('[submit-form]').click(function() {
    //     $('[error-message]').remove()
    //     const form = $(this).closest('form')[0]; // Get the closest form element
    //     if (form.checkValidity()) {
    //         // If the form is valid, submit it
    //         form.submit();
    //     } else {
    //         // If the form is not valid, trigger validation messages
    //         $(form).find('select[required]').each(function() {
    //             let errorMessage = $(this).siblings('#error-message');
    
    //             if (!this.checkValidity()) {
    //                 // If the error message span doesn't exist, create it
    //                 if (errorMessage.length === 0) {
    //                     errorMessage = $('<span error-message style="color: red;">This field is required</span>');
    //                     $(this).parent().append(errorMessage);
    //                 } else {
    //                     errorMessage.show();
    //                 }
    //             } else {
    //                 errorMessage.hide();
    //             }
    //         });
    
    //         form.reportValidity(); // Display native validation messages
    //     }
    // });
    
    // document.querySelectorAll('form[form-select-inside]').forEach(function(form) {
    //     form.addEventListener('submit', function(event) {
    //         const select = form.querySelector('select[required]');
    //         let errorMessage = form.querySelector('#error-message');
    
    //         if (select && !select.checkValidity()) {
    //             event.preventDefault(); // Prevent default form submission
    
    //             if (!errorMessage) {
    //                 // Dynamically create the span if it doesn't exist
    //                 errorMessage = document.createElement('span');
    //                 errorMessage.id = 'error-message';
    //                 errorMessage.style.color = 'red';
    //                 errorMessage.textContent = 'Please select an option.';
    
    //                 // Insert the span after the select element
    //                 select.insertAdjacentElement('afterend', errorMessage);
    //             } else {
    //                 // Update the existing error message
    //                 errorMessage.style.display = 'inline';
    //             }
    //         } else if (errorMessage) {
    //             // Hide the error message if the select is valid
    //             errorMessage.style.display = 'none';
    //         }
    //     });
    // });
    
}

function toggle_side_menu() {
    $('[toggle-side-menu]').click(function(){
        $('body').toggleClass('sidemenu-active');
    })
}

function successMsg(msg,duration = 3000) {
    Toastify({
        text: msg,
        duration: 3000,
        close: true,
        gravity: "top",
        position: "center",
        backgroundColor:'#05b305',
    }).showToast();
}